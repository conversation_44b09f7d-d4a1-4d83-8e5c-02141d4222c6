import type { Edge, Node } from 'reactflow'

export type FlowMode = 'customerPersona' | 'plannerFreeKick'

export interface ModeNodeData {
  mode: FlowMode
  timeRange?: {
    startDate: string
    endDate: string
  }
  timePoint?: string
  chatId?: string
  chatIds?: string[]
  roundId?: string
  isDanmu?: boolean
  mainTask?: string
}

export interface InputNodeData {
  generatorType?: string
  params?: Record<string, unknown>
}

export interface PromptNodeData {
  promptTemplate: string
  chatIds: string[]
  inputTransformer?: string
}

export interface PromptBatchResult {
  input: unknown
  prompt: string
  results: Array<{
    chatId: string
    output: string
  }>
}

export interface OutputNodeData {
  trace?: ExecutionTrace
  meta?: Record<string, unknown>
}

export type AppNode = Node<ModeNodeData | InputNodeData | PromptNodeData | OutputNodeData>

export interface FlowPayload {
  nodes: AppNode[]
  edges: Edge[]
}

export interface ExecutionStep {
  nodeId: string
  nodeType: string
  inputData: unknown
  outputData: unknown
  error?: string
}

export type ExecutionTrace = ExecutionStep[]
