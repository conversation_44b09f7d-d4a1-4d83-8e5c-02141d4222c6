{"name": "admin_platform", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start -H 0.0.0.0", "lint": "next lint"}, "dependencies": {"material-react-table": "^3.2.1", "motion": "^12.6.0", "next": "15.2.3", "next-auth": "^5.0.0-beta.28", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-toastify": "^11.0.5", "reactflow": "^11.11.4", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.0.14", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "daisyui": "^5.0.6", "eslint": "^9", "eslint-config-next": "15.2.3", "prettier": "3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4.0.14", "typescript": "^5"}}