'use client'

import { useMemo, useState } from 'react'
import dayjs from 'dayjs'
import React<PERSON>low, { Background, Controls, type Edge, type Node, type ReactFlowInstance } from 'reactflow'
import 'reactflow/dist/style.css'

import ModeNode from '@/app/component/prompt_flow/ModeNode'
import InputNode from '@/app/component/prompt_flow/InputNode'
import OutputNode from '@/app/component/prompt_flow/OutputNode'
import type { ExecutionTrace, ModeNodeData } from '@/types/flow'

const MODE_NODE_ID = 'mode-node'
const INPUT_NODE_ID = 'input-node'
const OUTPUT_NODE_ID = 'output-node'

const nodeTypes = {
  modeNode: ModeNode,
  inputNode: InputNode,
  outputNode: OutputNode,
}

const DEFAULT_RANGE = {
  startDate: dayjs().subtract(7, 'day').format('YYYY-MM-DD'),
  endDate: dayjs().format('YYYY-MM-DD'),
}

const DEFAULT_MODE_STATE: ModeNodeData = {
  mode: 'customerPersona',
  chatId: '',
  chatIds: [],
  timeRange: { ...DEFAULT_RANGE },
  timePoint: 'day1',
  isDanmu: false,
}

interface PersonaRunMeta {
  chatId: string
  portrait: Record<string, unknown>
  rawOutput: string
  chatHistory: unknown
  roundId: string
}

interface PlannerRunMeta {
  chatId: string
  mainTask: string
  context: Record<string, unknown>
  plannerOutput: unknown
  freeKickOutput: unknown
  roundId: string
}

function deriveChatIds(modeState: ModeNodeData): string[] {
  if (Array.isArray(modeState.chatIds) && modeState.chatIds.length > 0) {
    return modeState.chatIds
  }
  if (!modeState.chatId) return []
  return modeState.chatId
    .split(/\r?\n/)
    .map((item) => item.trim())
    .filter((item) => item.length > 0)
}

function buildRequestParams(modeState: ModeNodeData) {
  const chatIds = deriveChatIds(modeState)
  if (modeState.mode === 'customerPersona') {
    return {
      chatIds,
      startDate: modeState.timeRange?.startDate,
      endDate: modeState.timeRange?.endDate,
      isDanmu: modeState.isDanmu,
    }
  }

  return {
    chatIds,
    timePoint: modeState.timePoint,
    mainTask: modeState.mainTask,
  }
}

function createEdges(): Edge[] {
  return [
    { id: 'e-mode-input', source: MODE_NODE_ID, target: INPUT_NODE_ID, animated: true },
    { id: 'e-input-output', source: INPUT_NODE_ID, target: OUTPUT_NODE_ID, animated: true },
  ]
}

function createNodes(
  modeState: ModeNodeData,
  executionTrace: ExecutionTrace,
  responseMeta: Record<string, unknown>,
  handlers: {
    onModeChange: (patch: Partial<ModeNodeData>) => void
  },
): Node[] {
  const nodes: Node[] = []
  const requestParams = buildRequestParams(modeState)

  nodes.push({
    id: MODE_NODE_ID,
    type: 'modeNode',
    position: { x: 0, y: 0 },
    data: {
      ...modeState,
      onChange: handlers.onModeChange,
    },
  })

  const personaDetails = (responseMeta.personaRuns as PersonaRunMeta[] | undefined)?.map((run) => ({
    chatId: run.chatId,
    chatHistory: run.chatHistory,
    roundId: run.roundId,
  }))

  const plannerDetails = (responseMeta.plannerRuns as PlannerRunMeta[] | undefined)?.map((run) => ({
    chatId: run.chatId,
    context: run.context,
    mainTask: run.mainTask,
    roundId: run.roundId,
  }))

  const inputDetails = modeState.mode === 'customerPersona' ? personaDetails ?? null : plannerDetails ?? null

  nodes.push({
    id: INPUT_NODE_ID,
    type: 'inputNode',
    position: { x: 320, y: 0 },
    data: {
      title: '输入概览',
      params: requestParams,
      description:
        modeState.mode === 'customerPersona'
          ? '按照提供的时间范围提取真实聊天记录，用于客户画像调试。'
          : '聚合客户画像、行为与阶段上下文，用于 Planner + FreeKick 调试。',
      details: inputDetails,
    },
  })

  nodes.push({
    id: OUTPUT_NODE_ID,
    type: 'outputNode',
    position: { x: 760, y: 0 },
    data: {
      trace: executionTrace,
      meta: responseMeta,
    },
  })

  return nodes
}

export default function PromptFlowPage() {
  const [modeState, setModeState] = useState<ModeNodeData>(DEFAULT_MODE_STATE)
  const [executionTrace, setExecutionTrace] = useState<ExecutionTrace>([])
  const [responseMeta, setResponseMeta] = useState<Record<string, unknown>>({})
  const [isRunning, setIsRunning] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [reactFlowInstance, setReactFlowInstance] = useState<ReactFlowInstance | null>(null)

  const handleModeChange = (patch: Partial<ModeNodeData>) => {
    setModeState((previous) => {
      const next: ModeNodeData = { ...previous, ...patch }
      let modeChanged = false

      if (typeof patch.mode !== 'undefined' && patch.mode !== previous.mode) {
        modeChanged = true
      }

      if (modeChanged) {
        if (next.mode === 'customerPersona') {
          next.timeRange = next.timeRange ?? { ...DEFAULT_RANGE }
          next.timePoint = undefined
          next.mainTask = undefined
        } else {
          next.timeRange = undefined
          next.timePoint = next.timePoint ?? 'day1'
        }
        setExecutionTrace([])
        setResponseMeta({})
      }

      if (patch.timeRange) {
        next.timeRange = { ...previous.timeRange, ...patch.timeRange }
      }

      if (typeof patch.chatIds !== 'undefined') {
        next.chatIds = patch.chatIds
      }

      return next
    })
  }

  const nodes = useMemo(
    () =>
      createNodes(modeState, executionTrace, responseMeta, {
        onModeChange: handleModeChange,
      }),
    [modeState, executionTrace, responseMeta],
  )

  const edges = useMemo(() => createEdges(), [])

  const handleRun = async () => {
    setIsRunning(true)
    setError(null)
    try {
      const requestParams = buildRequestParams(modeState)
      if (!requestParams.chatIds || requestParams.chatIds.length === 0) {
        throw new Error('请至少输入一个 chatId。')
      }

      const body: Record<string, unknown> = {
        mode: modeState.mode,
        params: requestParams,
      }

      const response = await fetch('/api/execute-flow', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(body),
      })

      const result = await response.json()
      if (!response.ok) {
        throw new Error(result?.error ?? '执行失败，请稍后重试。')
      }

      setExecutionTrace(result.trace ?? [])
      setResponseMeta(result.meta ?? {})
      requestAnimationFrame(() => {
        reactFlowInstance?.fitView({ padding: 0.2, duration: 400 })
      })
    } catch (err) {
      const message = err instanceof Error ? err.message : '执行失败。'
      setError(message)
    } finally {
      setIsRunning(false)
    }
  }

  return (
    <div className="flex h-full flex-col gap-4 p-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold text-slate-800">Prompt 调试平台</h1>
          <p className="text-sm text-slate-500">
            选择场景后拉取真实数据，验证客户画像与 Planner + FreeKick 的完整链路。
          </p>
        </div>
        <div className="flex items-center gap-3">
          {error && <span className="text-sm text-red-600">{error}</span>}
          <button
            type="button"
            className="rounded bg-indigo-600 px-4 py-2 text-sm font-semibold text-white disabled:opacity-60"
            onClick={handleRun}
            disabled={isRunning}
          >
            {isRunning ? '执行中...' : '运行流程'}
          </button>
        </div>
      </div>

      <div className="h-[calc(100vh-200px)] rounded-md border border-slate-200">
        <ReactFlow
          nodes={nodes}
          edges={edges}
          nodeTypes={nodeTypes}
          nodesDraggable
          nodesConnectable={false}
          elementsSelectable
          fitView
          className="bg-slate-50"
          onInit={setReactFlowInstance}
          proOptions={{ hideAttribution: true }}
        >
          <Background gap={16} color="#CBD5F5" />
          <Controls showInteractive={false} />
        </ReactFlow>
      </div>
    </div>
  )
}
