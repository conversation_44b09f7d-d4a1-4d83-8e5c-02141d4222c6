'use client'

import { useMemo, useState } from 'react'
import dayjs from 'dayjs'
import React<PERSON>low, { Background, Controls, type Edge, type Node } from 'reactflow'
import 'reactflow/dist/style.css'

import ModeNode from '@/app/component/prompt_flow/ModeNode'
import InputNode from '@/app/component/prompt_flow/InputNode'
import OutputNode from '@/app/component/prompt_flow/OutputNode'
import type {
  ExecutionTrace,
  FlowMode,
  ModeNodeData,
} from '@/types/flow'

const MODE_NODE_ID = 'mode-node'
const INPUT_NODE_ID = 'input-node'
const OUTPUT_NODE_ID = 'output-node'

const DEFAULT_RANGE = {
  startDate: dayjs().subtract(7, 'day').format('YYYY-MM-DD'),
  endDate: dayjs().format('YYYY-MM-DD'),
}

const DEFAULT_MODE_STATE: ModeNodeData = {
  mode: 'customerPersona',
  timeRange: { ...DEFAULT_RANGE },
}



const nodeTypes = {
  modeNode: ModeNode,
  inputNode: InputNode,
  outputNode: OutputNode,
}

function buildInputDescription(mode: FlowMode) {
  if (mode === 'customerPersona') {
    return '为指定时间范围生成 Mock 客户画像样本，用于单 Prompt 测试。'
  }
  return '生成 Planner + FreeKick 所需的阶段信息与上下文，用于级联 Prompt 调试。'
}

function toMockCurrentTime(modeState: ModeNodeData) {
  if (modeState.mode === 'customerPersona') {
    const end = modeState.timeRange?.endDate
    if (end && dayjs(end).isValid()) {
      return dayjs(end).endOf('day').toISOString()
    }
    return dayjs().toISOString()
  }
  const point = modeState.timePoint ?? 'day1'
  const offsets: Record<string, number> = {
    day1: 0,
    day2: 1,
    day3: 2,
    day4: 3,
  }
  const base = dayjs().startOf('day')
  return base.add(offsets[point] ?? 0, 'day').add(9, 'hour').toISOString()
}

function buildInputParams(modeState: ModeNodeData) {
  const mockCurrentTime = toMockCurrentTime(modeState)
  if (modeState.mode === 'customerPersona') {
    return {
      startDate: modeState.timeRange?.startDate,
      endDate: modeState.timeRange?.endDate,
      mockCurrentTime,
    }
  }
  return {
    timePoint: modeState.timePoint ?? 'day1',
    mockCurrentTime,
  }
}

function buildRequestParams(modeState: ModeNodeData) {
  const chatIds = modeState.chatId
    ? modeState.chatId
      .split(/\r?\n/)
      .map((item) => item.trim())
      .filter((item) => item.length > 0)
    : []

  if (modeState.mode === 'customerPersona') {
    return {
      chatIds,
      startDate: modeState.timeRange?.startDate,
      endDate: modeState.timeRange?.endDate,
      isDanmu: modeState.isDanmu,
      roundId: modeState.roundId,
    }
  }

  return {
    chatIds,
    timePoint: modeState.timePoint,
    mainTaskOverride: modeState.mainTask,
    roundId: modeState.roundId,
  }
}

function createNodes(
  modeState: ModeNodeData,
  executionTrace: ExecutionTrace,
  responseMeta: Record<string, unknown>,
  handlers: {
    onModeChange: (patch: Partial<ModeNodeData>) => void
  },
): Node[] {
  const nodes: Node[] = []
  const requestParams = buildRequestParams(modeState)

  nodes.push({
    id: MODE_NODE_ID,
    type: 'modeNode',
    position: { x: 0, y: 0 },
    data: {
      ...modeState,
      onChange: handlers.onModeChange,
    },
  })

  nodes.push({
    id: INPUT_NODE_ID,
    type: 'inputNode',
    position: { x: 320, y: 0 },
    data: {
      title: '输入概览',
      params: requestParams,
      description:
        modeState.mode === 'customerPersona'
          ? '按照提供的时间范围提取真实聊天记录，用于客户画像调试。'
          : '聚合客户画像、行为与阶段上下文，用于 Planner + FreeKick 调试。',
    },
  })

  nodes.push({
    id: OUTPUT_NODE_ID,
    type: 'outputNode',
    position: { x: 760, y: 0 },
    data: {
      trace: executionTrace,
      meta: responseMeta,
    },
  })

  return nodes
}

function createEdges(): Edge[] {
  return [
    { id: 'e-mode-input', source: MODE_NODE_ID, target: INPUT_NODE_ID, animated: true },
    { id: 'e-input-output', source: INPUT_NODE_ID, target: OUTPUT_NODE_ID, animated: true },
  ]
}

function sanitizeData(data: unknown) {
  return JSON.parse(
    JSON.stringify(data, (_key, value) => {
      if (typeof value === 'function') {
        return undefined
      }
      return value
    }),
  )
}

export default function PromptFlowPage() {
  const [modeState, setModeState] = useState<ModeNodeData>(DEFAULT_MODE_STATE)
  const [executionTrace, setExecutionTrace] = useState<ExecutionTrace>([])
  const [responseMeta, setResponseMeta] = useState<Record<string, unknown>>({})
  const [isRunning, setIsRunning] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleModeChange = (patch: Partial<ModeNodeData>) => {
    setModeState((previous) => {
      const next: ModeNodeData = { ...previous }
      let modeChanged = false

      if (typeof patch.mode !== 'undefined' && patch.mode !== previous.mode) {
        modeChanged = true
        next.mode = patch.mode
      } else if (typeof patch.mode !== 'undefined') {
        next.mode = patch.mode
      }

      if (patch.timeRange) {
        next.timeRange = { ...next.timeRange, ...patch.timeRange }
      }

      if (typeof patch.timePoint !== 'undefined') {
        next.timePoint = patch.timePoint
      }

      if (modeChanged) {
        if (next.mode === 'customerPersona') {
          next.timeRange = {
            startDate: patch.timeRange?.startDate ?? DEFAULT_RANGE.startDate,
            endDate: patch.timeRange?.endDate ?? DEFAULT_RANGE.endDate,
          }
          next.timePoint = undefined
        } else {
          next.timeRange = undefined
          next.timePoint = patch.timePoint ?? 'day1'
        }
      }

      if (!next.timeRange && next.mode === 'customerPersona') {
        next.timeRange = { ...DEFAULT_RANGE }
      }
      if (!next.timePoint && next.mode === 'plannerFreeKick') {
        next.timePoint = 'day1'
      }

      return next
    })

    if (typeof patch.mode !== 'undefined') {
      setExecutionTrace([])
      setResponseMeta({})
    }
  }

  const nodes = useMemo(
    () =>
      createNodes(modeState, executionTrace, responseMeta, {
        onModeChange: handleModeChange,
      }),
    [modeState, executionTrace, responseMeta],
  )

  const edges = useMemo(() => createEdges(), [])

  const handleRun = async () => {
    setIsRunning(true)
    setError(null)
    try {
      const requestParams = buildRequestParams(modeState)
      if (!requestParams.chatIds || requestParams.chatIds.length === 0) {
        throw new Error('请至少输入一个 chatId。')
      }

      const body: Record<string, unknown> = {
        mode: modeState.mode,
        params: requestParams,
      }

      const response = await fetch('/api/execute-flow', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(body),
      })

      const result = await response.json()
      if (!response.ok) {
        throw new Error(result?.error ?? '执行失败，请稍后重试。')
      }

      setExecutionTrace(result.trace ?? [])
      setResponseMeta(result.meta ?? {})
    } catch (err) {
      const message = err instanceof Error ? err.message : '执行失败。'
      setError(message)
    } finally {
      setIsRunning(false)
    }
  }

  return (
    <div className="flex h-full flex-col gap-4 p-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold text-slate-800">Prompt 调试平台</h1>
          <p className="text-sm text-slate-500">
            按照模式自动生成输入，串联单 / 多 Prompt，并完整返回执行链路。
          </p>
        </div>
        <div className="flex items-center gap-3">
          {error && <span className="text-sm text-red-600">{error}</span>}
          <button
            type="button"
            className="rounded bg-indigo-600 px-4 py-2 text-sm font-semibold text-white disabled:opacity-60"
            onClick={handleRun}
            disabled={isRunning}
          >
            {isRunning ? '执行中...' : '运行流程'}
          </button>
        </div>
      </div>

      <div className="h-[70vh] rounded-md border border-slate-200">
        <ReactFlow
          nodes={nodes}
          edges={edges}
          nodeTypes={nodeTypes}
          nodesDraggable
          nodesConnectable={false}
          elementsSelectable
          fitView
          panOnDrag={[1, 2]}
          className="bg-slate-50"
          proOptions={{ hideAttribution: true }}
        >
          <Background gap={16} color="#CBD5F5" />
          <Controls showInteractive={false} />
        </ReactFlow>
      </div>
    </div>
  )
}
