'use client'

import { memo } from 'react'
import { <PERSON>le, Position, type NodeProps } from 'reactflow'
import type { FlowMode, ModeNodeData } from '@/types/flow'

interface ModeNodeRenderData extends ModeNodeData {
  onChange?: (patch: Partial<ModeNodeData>) => void
}

const MODE_LABEL: Record<FlowMode, string> = {
  customerPersona: '客户画像测试',
  plannerFreeKick: 'Planner + FreeKick 测试',
}

const PlannerOptions = ['day1', 'day2', 'day3', 'day4']

function ModeNode({ data }: NodeProps<ModeNodeRenderData>) {
  const handleChatIdChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    const rawValue = event.target.value
    const chatIds = rawValue
      .split(/\r?\n/)
      .map((item) => item.trim())
      .filter((item) => item.length > 0)
    data.onChange?.({ chatId: rawValue, chatIds })
  }

  const handleDanmuToggle = (event: React.ChangeEvent<HTMLInputElement>) => {
    data.onChange?.({ isDanmu: event.target.checked })
  }

  const handleMainTaskChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    data.onChange?.({ mainTask: event.target.value })
  }

  const handleModeSwitch = (event: React.ChangeEvent<HTMLSelectElement>) => {
    data.onChange?.({ mode: event.target.value as FlowMode })
  }

  const handleDateChange = (field: 'startDate' | 'endDate') =>
    (event: React.ChangeEvent<HTMLInputElement>) => {
      data.onChange?.({ timeRange: { [field]: event.target.value } as ModeNodeData['timeRange'] })
    }

  const handleTimePointChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    data.onChange?.({ timePoint: event.target.value })
  }

  return (
    <div className="min-w-[280px] rounded-md border border-slate-300 bg-white p-3 shadow-sm">
      <Handle type="source" position={Position.Right} />
      <h3 className="mb-2 font-semibold text-slate-700">调试模式</h3>
      <label className="mb-3 flex flex-col gap-1 text-sm text-slate-600">
        <span>请选择调试场景</span>
        <select
          className="rounded border border-slate-300 px-2 py-1 text-sm"
          value={data.mode}
          onChange={handleModeSwitch}
        >
          {Object.entries(MODE_LABEL).map(([value, label]) => (
            <option key={value} value={value}>
              {label}
            </option>
          ))}
        </select>
      </label>

      <label className="mb-3 flex flex-col gap-1 text-sm text-slate-600">
        <span>客户 Chat ID（支持多行批量）</span>
        <textarea
          className="h-24 resize-none rounded border border-slate-300 p-2 text-sm font-mono"
          placeholder="每行一个 chatId"
          value={data.chatId ?? ''}
          onChange={handleChatIdChange}
        />
        <span className="text-xs text-slate-400">round_id 将在执行后自动生成并显示在结果中。</span>
      </label>

      {data.mode === 'customerPersona' ? (
        <div className="flex flex-col gap-2 text-sm text-slate-600">
          <label className="flex items-center gap-2 text-sm text-slate-600">
            <input
              type="checkbox"
              className="checkbox checkbox-sm"
              checked={Boolean(data.isDanmu)}
              onChange={handleDanmuToggle}
            />
            <span>弹幕模式</span>
          </label>
          <span className="font-medium">时间范围</span>
          <label className="flex flex-col gap-1">
            <span>开始日期</span>
            <input
              type="date"
              className="rounded border border-slate-300 px-2 py-1 text-sm"
              value={data.timeRange?.startDate ?? ''}
              onChange={handleDateChange('startDate')}
            />
          </label>
          <label className="flex flex-col gap-1">
            <span>结束日期</span>
            <input
              type="date"
              className="rounded border border-slate-300 px-2 py-1 text-sm"
              value={data.timeRange?.endDate ?? ''}
              onChange={handleDateChange('endDate')}
            />
          </label>
        </div>
      ) : (
        <div className="flex flex-col gap-3 text-sm text-slate-600">
          <label className="flex flex-col gap-1">
            <span className="font-medium">选择测试时间点</span>
            <select
              className="rounded border border-slate-300 px-2 py-1 text-sm"
              value={data.timePoint ?? 'day1'}
              onChange={handleTimePointChange}
            >
              {PlannerOptions.map((option) => (
                <option key={option} value={option}>
                  {option}
                </option>
              ))}
            </select>
          </label>
          <label className="flex flex-col gap-1">
            <span className="font-medium">自定义任务 (可选)</span>
            <input
              className="rounded border border-slate-300 px-2 py-1 text-sm"
              placeholder="覆盖 Planner 产出的任务"
              value={data.mainTask ?? ''}
              onChange={handleMainTaskChange}
            />
          </label>
        </div>
      )}
    </div>
  )
}

export default memo(ModeNode)
