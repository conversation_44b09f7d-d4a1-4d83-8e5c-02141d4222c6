'use client'

import { memo } from 'react'
import { <PERSON><PERSON>, Position, type NodeProps } from 'reactflow'
import type { InputNodeData } from '@/types/flow'

interface InputNodeRenderData extends InputNodeData {
  title?: string
  description?: string
  details?: unknown
}

function InputNode({ data }: NodeProps<InputNodeRenderData>) {
  const entries = Object.entries(data.params ?? {})

  return (
    <div className="min-w-[260px] rounded-md border border-slate-300 bg-white p-3 shadow-sm">
      <Handle type="target" position={Position.Left} />
      <Handle type="source" position={Position.Right} />
      <h3 className="mb-1 font-semibold text-slate-700">{data.title ?? '输入概况'}</h3>
      {data.description && <p className="mb-3 text-sm text-slate-600">{data.description}</p>}
      {entries.length > 0 && (
        <table className="w-full table-auto border-collapse text-xs text-slate-500">
          <tbody>
            {entries.map(([key, value]) => (
              <tr key={key}>
                <td className="w-24 border border-slate-200 px-2 py-1 font-medium text-slate-600">
                  {key}
                </td>
                <td className="border border-slate-200 px-2 py-1">
                  {typeof value === 'string' ? value : JSON.stringify(value)}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      )}
      {Array.isArray(data.details) && data.details.length > 0 && (
        <div className="mt-3 space-y-2 text-xs text-slate-600">
          {data.details.map((item: any, index: number) => (
            <details key={(item.chatId ?? item.roundId ?? index).toString()} className="rounded border border-slate-200 bg-slate-50 p-2">
              <summary className="cursor-pointer font-medium text-slate-700">
                {item.chatId ?? '上下文'} {item.roundId ? `(round_id: ${item.roundId})` : ''}
              </summary>
              <pre className="mt-2 overflow-auto whitespace-pre-wrap break-words bg-white p-2 text-[11px] text-slate-700">
                {JSON.stringify(item.chatHistory ?? item.context ?? item, null, 2)}
              </pre>
            </details>
          ))}
        </div>
      )}
    </div>
  )
}

export default memo(InputNode)
