'use client'

import { memo } from 'react'
import { Handle, Position, type NodeProps } from 'reactflow'
import type { ExecutionTrace, OutputNodeData } from '@/types/flow'

interface OutputNodeRenderData extends OutputNodeData {
  onClear?: () => void
}

const langsmithTemplate = process.env.NEXT_PUBLIC_LANGSMITH_URL_TEMPLATE ?? 'https://smith.langchain.com/public?query=%ROUND_ID%'

function buildLangsmithLink(roundId?: string) {
  if (!roundId) return null
  return langsmithTemplate.replace('%ROUND_ID%', encodeURIComponent(roundId))
}

function OutputNode({ data }: NodeProps<OutputNodeRenderData>) {
  const trace = data.trace ?? []
  const meta = data.meta
  const personaRuns = Array.isArray(meta?.personaRuns) ? (meta.personaRuns as Array<any>) : null
  const plannerRuns = Array.isArray(meta?.plannerRuns) ? (meta.plannerRuns as Array<any>) : null

  return (
    <div className="min-w-[240px] rounded-md border border-emerald-200 bg-emerald-50 p-3 shadow-sm">
      <Handle type="target" position={Position.Left} />
      <h3 className="mb-2 font-semibold text-emerald-700">执行结果</h3>
      {personaRuns && personaRuns.length > 0 && (
        <div className="mb-4 space-y-2 text-xs text-slate-700">
          <span className="font-semibold text-emerald-600">客户画像结果</span>
          <table className="w-full table-auto border-collapse text-left">
            <thead>
              <tr className="bg-emerald-100 text-slate-700">
                <th className="border border-emerald-200 px-2 py-1">Chat ID / round_id</th>
                <th className="border border-emerald-200 px-2 py-1">输入（聊天记录）</th>
                <th className="border border-emerald-200 px-2 py-1">输出（画像）</th>
                <th className="border border-emerald-200 px-2 py-1">操作</th>
              </tr>
            </thead>
            <tbody>
              {personaRuns.map((run) => {
                const link = buildLangsmithLink(run.roundId)
                return (
                  <tr key={run.chatId} className="align-top">
                    <td className="border border-emerald-200 px-2 py-1">
                      <div className="flex flex-col">
                        <span className="font-medium text-slate-700">{run.chatId}</span>
                        {run.roundId && (
                          <span className="text-[11px] text-slate-500">round_id: {run.roundId}</span>
                        )}
                      </div>
                    </td>
                    <td className="border border-emerald-200 px-2 py-1">
                      <details className="rounded border border-slate-200 bg-white p-2">
                        <summary className="cursor-pointer text-emerald-600">查看输入</summary>
                        <pre className="mt-2 overflow-auto whitespace-pre-wrap break-words bg-slate-900 p-2 text-[11px] text-white">
                          {JSON.stringify(run.chatHistory, null, 2)}
                        </pre>
                      </details>
                    </td>
                    <td className="border border-emerald-200 px-2 py-1">
                      <details className="rounded border border-slate-200 bg-white p-2">
                        <summary className="cursor-pointer text-emerald-600">查看输出</summary>
                        <pre className="mt-2 overflow-auto whitespace-pre-wrap break-words bg-slate-900 p-2 text-[11px] text-white">
                          {JSON.stringify(run.portrait, null, 2)}
                        </pre>
                      </details>
                    </td>
                    <td className="border border-emerald-200 px-2 py-1">
                      {link ? (
                        <a
                          href={link}
                          target="_blank"
                          rel="noreferrer"
                          className="inline-flex items-center gap-1 rounded border border-emerald-200 px-2 py-1 text-[11px] text-emerald-700 hover:bg-emerald-100"
                        >
                          LangSmith
                        </a>
                      ) : (
                        <span className="text-[11px] text-slate-400">未生成</span>
                      )}
                    </td>
                  </tr>
                )
              })}
            </tbody>
          </table>
        </div>
      )}

      {plannerRuns && plannerRuns.length > 0 && (
        <div className="mb-4 space-y-2 text-xs text-slate-700">
          <span className="font-semibold text-emerald-600">Planner + FreeKick 结果</span>
          <table className="w-full table-auto border-collapse text-left">
            <thead>
              <tr className="bg-emerald-100 text-slate-700">
                <th className="border border-emerald-200 px-2 py-1">Chat ID / round_id</th>
                <th className="border border-emerald-200 px-2 py-1">输入（上下文）</th>
                <th className="border border-emerald-200 px-2 py-1">Planner 输出</th>
                <th className="border border-emerald-200 px-2 py-1">FreeKick 输出</th>
                <th className="border border-emerald-200 px-2 py-1">操作</th>
              </tr>
            </thead>
            <tbody>
              {plannerRuns.map((run) => {
                const link = buildLangsmithLink(run.roundId)
                return (
                  <tr key={run.chatId} className="align-top">
                    <td className="border border-emerald-200 px-2 py-1">
                      <div className="flex flex-col">
                        <span className="font-medium text-slate-700">{run.chatId}</span>
                        {run.roundId && (
                          <span className="text-[11px] text-slate-500">round_id: {run.roundId}</span>
                        )}
                        {run.mainTask && (
                          <span className="text-[11px] text-slate-500">任务：{run.mainTask}</span>
                        )}
                      </div>
                    </td>
                    <td className="border border-emerald-200 px-2 py-1">
                      <details className="rounded border border-slate-200 bg-white p-2">
                        <summary className="cursor-pointer text-emerald-600">查看输入</summary>
                        <pre className="mt-2 overflow-auto whitespace-pre-wrap break-words bg-slate-900 p-2 text-[11px] text-white">
                          {JSON.stringify(run.context, null, 2)}
                        </pre>
                      </details>
                    </td>
                    <td className="border border-emerald-200 px-2 py-1">
                      <details className="rounded border border-slate-200 bg-white p-2">
                        <summary className="cursor-pointer text-emerald-600">查看 Planner</summary>
                        <pre className="mt-2 overflow-auto whitespace-pre-wrap break-words bg-slate-900 p-2 text-[11px] text-white">
                          {JSON.stringify(run.plannerOutput, null, 2)}
                        </pre>
                      </details>
                    </td>
                    <td className="border border-emerald-200 px-2 py-1">
                      <details className="rounded border border-slate-200 bg-white p-2">
                        <summary className="cursor-pointer text-emerald-600">查看 FreeKick</summary>
                        <pre className="mt-2 overflow-auto whitespace-pre-wrap break-words bg-slate-900 p-2 text-[11px] text-white">
                          {JSON.stringify(run.freeKickOutput, null, 2)}
                        </pre>
                      </details>
                    </td>
                    <td className="border border-emerald-200 px-2 py-1">
                      {link ? (
                        <a
                          href={link}
                          target="_blank"
                          rel="noreferrer"
                          className="inline-flex items-center gap-1 rounded border border-emerald-200 px-2 py-1 text-[11px] text-emerald-700 hover:bg-emerald-100"
                        >
                          LangSmith
                        </a>
                      ) : (
                        <span className="text-[11px] text-slate-400">未生成</span>
                      )}
                    </td>
                  </tr>
                )
              })}
            </tbody>
          </table>
        </div>
      )}

      {meta && !personaRuns && !plannerRuns && Object.keys(meta).length > 0 && (
        <details className="mb-3 rounded border border-emerald-200 bg-white p-2 text-xs text-slate-700">
          <summary className="cursor-pointer font-medium text-emerald-600">附加信息</summary>
          <pre className="mt-2 whitespace-pre-wrap break-words text-[11px]">
            {JSON.stringify(meta, null, 2)}
          </pre>
        </details>
      )}
      {trace.length === 0 ? (
        <p className="text-sm text-emerald-700">执行后可在此查看完整链路。</p>
      ) : (
        <div className="max-h-64 overflow-auto space-y-2 text-xs text-slate-700">
          {trace.map((step) => (
            <details key={step.nodeId} className="rounded border border-emerald-200 bg-white p-2">
              <summary className="cursor-pointer font-semibold text-emerald-600">
                {step.nodeId} ({step.nodeType})
              </summary>
              <div className="mt-2 space-y-2">
                <div>
                  <span className="font-medium text-slate-600">输入</span>
                  <pre className="mt-1 max-h-32 overflow-auto rounded bg-slate-900 p-2 text-[10px] text-white">
                    {JSON.stringify(step.inputData, null, 2)}
                  </pre>
                </div>
                <div>
                  <span className="font-medium text-slate-600">输出</span>
                  <pre className="mt-1 max-h-32 overflow-auto rounded bg-slate-900 p-2 text-[10px] text-white">
                    {JSON.stringify(step.outputData, null, 2)}
                  </pre>
                </div>
                {step.error && (
                  <div className="rounded border border-red-300 bg-red-50 p-2 text-[11px] text-red-700">
                    {step.error}
                  </div>
                )}
              </div>
            </details>
          ))}
        </div>
      )}
    </div>
  )
}

export default memo(OutputNode)
