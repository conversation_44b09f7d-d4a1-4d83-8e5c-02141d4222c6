import { NextRequest, NextResponse } from 'next/server'
import { runCustomerPersonaFlow } from '@/lib/flow/customer-persona'
import { runPlanner<PERSON>reeKickFlow } from '@/lib/flow/planner-freekick'
import type { ExecutionTrace, FlowMode } from '@/types/flow'

interface ExecuteRequest {
  mode: FlowMode
  params: Record<string, unknown>
}

function parseChatIds(value: unknown): string[] {
  if (Array.isArray(value)) {
    return value
      .map((item) => String(item ?? '').trim())
      .filter((item) => item.length > 0)
  }

  const raw = String(value ?? '')
  return raw
    .split(/\r?\n/)
    .map((item) => item.trim())
    .filter((item) => item.length > 0)
}

export async function POST(request: NextRequest) {
  try {
    const payload = (await request.json()) as ExecuteRequest
    const mode = payload?.mode
    const params = payload?.params ?? {}

    if (mode === 'customerPersona') {
      const chatIds = parseChatIds(params.chatIds ?? params.chatId)

      if (chatIds.length === 0) {
        return NextResponse.json({ error: '请至少提供一个 chatId。' }, { status: 400 })
      }

      const personaResults = await Promise.all(
        chatIds.map(async (chatId) => {
          const result = await runCustomerPersonaFlow({
            chatId,
            startDate: params.startDate ? String(params.startDate) : undefined,
            endDate: params.endDate ? String(params.endDate) : undefined,
            isDanmu: Boolean(params.isDanmu),
          })

          return {
            chatId,
            roundId: result.roundId,
            portrait: result.portrait,
            rawOutput: result.rawOutput,
            chatHistory: result.chatHistory,
            trace: result.trace,
          }
        }),
      )

      const aggregatedTrace: ExecutionTrace = personaResults.flatMap(({ chatId, trace }) =>
        trace.map((step) => ({
          ...step,
          nodeId: `${step.nodeId}#${chatId}`,
        })),
      )

      const personaRuns = personaResults.map(({ chatId, roundId, portrait, rawOutput, chatHistory }) => ({
        chatId,
        roundId,
        portrait,
        rawOutput,
        chatHistory,
      }))

      return NextResponse.json({
        trace: aggregatedTrace,
        meta: {
          personaRuns,
        },
      })
    }

    if (mode === 'plannerFreeKick') {
      const chatIds = parseChatIds(params.chatIds ?? params.chatId)

      if (chatIds.length === 0) {
        return NextResponse.json({ error: '请至少提供一个 chatId。' }, { status: 400 })
      }

      const plannerResults = await Promise.all(
        chatIds.map(async (chatId) => {
          const result = await runPlannerFreeKickFlow({
            chatId,
            timePoint: (params.timePoint as any) ?? 'day1',
          })

          return {
            chatId,
            roundId: result.roundId,
            mainTask: result.mainTask,
            context: result.context,
            plannerOutput: result.plannerOutput,
            freeKickOutput: result.freeKickOutput,
            trace: result.trace,
          }
        }),
      )

      const aggregatedTrace: ExecutionTrace = plannerResults.flatMap(({ chatId, trace }) =>
        trace.map((step) => ({
          ...step,
          nodeId: `${step.nodeId}#${chatId}`,
        })),
      )

      const plannerRuns = plannerResults.map(({ chatId, roundId, mainTask, context, plannerOutput, freeKickOutput }) => ({
        chatId,
        roundId,
        mainTask,
        context,
        plannerOutput,
        freeKickOutput,
      }))

      return NextResponse.json({
        trace: aggregatedTrace,
        meta: {
          plannerRuns,
        },
      })
    }

    return NextResponse.json({ error: '不支持的模式，请确认参数。' }, { status: 400 })
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : '未知错误'
    return NextResponse.json({ error: message }, { status: 500 })
  }
}
