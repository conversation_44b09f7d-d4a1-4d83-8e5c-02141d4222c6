import dayjs from 'dayjs'
import { randomUUID } from 'crypto'
import { PromptTemplate } from '@langchain/core/prompts'
import { getPrompt } from '../../../bot/service/moer/components/agent/prompt'
import { ContextBuilder } from '../../../bot/service/moer/components/agent/context'
import { DataService } from '../../../bot/service/moer/getter/getData'
import { TaskManager } from '../../../bot/service/moer/components/planner/task/task_manager'
import { CourseCompletionCache } from '../../../bot/service/moer/components/agent/course_completion_cache'
import { ChatStatStoreManager } from '../../../bot/service/moer/storage/chat_state_store'
import { SalesNodeHelper } from '../../../bot/service/moer/components/flow/helper/salesNodeHelper'
import { KnowledgeRag } from '../../../bot/service/moer/components/rag/planner/knowledge_rag/knowledge_rag'
import { LLM } from '../../../bot/lib/ai/llm/LLM'
import type { ExecutionTrace } from '@/types/flow'

export interface PlannerFreeKickParams {
  chatId: string
  timePoint: 'day1' | 'day2' | 'day3' | 'day4'
  roundId?: string
  plannerPromptOverride?: string
  freeKickPromptOverride?: string
  mainTaskOverride?: string
}

interface PlannerResult {
  trace: ExecutionTrace
  mainTask: string
  plannerOutput: any
  freeKickOutput: any
  context: Record<string, unknown>
  roundId: string
}

const TIMEPOINT_OFFSETS: Record<string, number> = {
  day1: 0,
  day2: 1,
  day3: 2,
  day4: 3,
}

async function buildMockTemporalInformation(chatId: string, timePoint: PlannerFreeKickParams['timePoint']) {
  try {
    const courseStart = await DataService.getCourseStartTime(chatId)
    const base = dayjs(courseStart).startOf('day')
    const offset = TIMEPOINT_OFFSETS[timePoint] ?? 0
    const simulated = base.add(offset, 'day').hour(20).minute(0).second(0)
    const weekday = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'][simulated.day()]

    return `调试模式模拟时间：${simulated.format('YYYY-MM-DD HH:mm:ss')} (${weekday})，对应 ${timePoint}`
  } catch (error) {
    const now = dayjs().hour(20).minute(0).second(0)
    return `调试模式模拟时间：${now.format('YYYY-MM-DD HH:mm:ss')}，对应 ${timePoint}`
  }
}

async function buildPlannerContext(chatId: string, timePoint: PlannerFreeKickParams['timePoint']) {
  await ChatStatStoreManager.initState(chatId)

  const [userSlots, memory, behavior, silent, baseTemporal, mockTemporal, scheduledTasks, stagePrompt] = await Promise.all([
    ContextBuilder.customerPortrait(chatId),
    ContextBuilder.getRecentMemories(chatId, 5),
    CourseCompletionCache.getCourseCompletionInfo(chatId),
    ContextBuilder.getCustomerSilentDuration(chatId, 24),
    ContextBuilder.temporalInformation(chatId),
    buildMockTemporalInformation(chatId, timePoint),
    TaskManager.getScheduledTasks(chatId),
    ContextBuilder.getCurrentStagePrompt(chatId),
  ])

  return {
    user_slots: userSlots,
    memory,
    user_behavior: behavior,
    silent_analyze: silent,
    current_time: `${baseTemporal}\n${mockTemporal}`,
    existing_task: scheduledTasks.map((task, index) => ({
      id: task.id ?? String(index + 1),
      description: task.description,
      time: task.send_time ?? '',
    })),
    stage_prompt: stagePrompt,
  }
}

async function runPlannerStep(params: PlannerFreeKickParams) {
  const { chatId, timePoint, plannerPromptOverride } = params
  const prompt = plannerPromptOverride ?? (await getPrompt('free-big-plan'))
  const context = await buildPlannerContext(chatId, timePoint)

  const rawOutput = await LLM.predict(
    prompt,
    {
      model: 'gpt-5',
      maxTokens: 4000,
      responseJSON: true,
      promptName: 'freeBigPlan',
      meta: { chat_id: chatId },
    },
    context,
  )

  let parsed: any = {}
  try {
    parsed = JSON.parse(rawOutput)
  } catch {
    parsed = {}
  }

  return {
    context,
    rawOutput,
    parsed,
  }
}

async function extractMaterialSuggestion(plan: string, chatId: string, roundId: string, timePoint: PlannerFreeKickParams['timePoint']) {
  const timeInfo = await buildMockTemporalInformation(chatId, timePoint)
  const template = `你是一个“素材调用助手”。
你的任务是：根据给定的 **目标描述/服务目的**，自动联想出可能需要的素材，并返回对应素材库中素材的 title 列表，用于后续搜索和调用。

# 目标描述
{plan}

# 当前时间
{timeStr}

# 参考素材（包括但不限于）
 - 入门营课程的服务相关：课程表（5天入门营课程安排总览，一般在客户刚加入的时候分享帮助了解），课程时刻表（针对每一节课每个时间段老师所讲内容的预告，一般在课前的时候使用），课程总结大纲，课程相关音频视频，上课方式指引等
 - 冥想相关科普信息补充：.....
 - 入门营学员异议处理：....
 - 入门营学习案例：.....
 - 高阶课学员案例：.....

# 使用规则：
1. 目标输入可能来自「课程规划器」生成的 task

2. 你需要基于目标的语义，匹配到最相关的素材标题。
   - **相似或重复的素材只保留一个**，确保输出列表中没有功能重叠的素材。
   - **每类需求只输出 1 个最能代表的素材**，保证覆盖目标需求即可。
   - 输出的素材标题必须是一句精简、完整的话，**不得包含括号说明**。

3. 输出时保持结构化：
   - \`reason\`（简要说明为什么选择这些素材，便于调试）
   - \`titles\`（一组 title，用于在素材库中搜索）

### 以JSON格式输出：
{{
    "reason": "简要逻辑"
    "titles": ["素材title1", "素材title2", "素材title3"],
}}
`

  const promptTemplate = PromptTemplate.fromTemplate(template)
  const prompt = await promptTemplate.format({ plan, timeStr: timeInfo })

  const response = await LLM.predict(prompt, {
    responseJSON: true,
    model: 'gpt-5-mini',
    reasoningEffort: 'low',
    meta: { chat_id: chatId, round_id: roundId, promptName: 'planner_material' },
  })

  try {
    return JSON.parse(response)
  } catch {
    return { reason: '解析失败', titles: [] }
  }
}

async function runFreeKickStep(params: PlannerFreeKickParams, mainTask: string, roundId: string) {
  const { chatId, freeKickPromptOverride } = params
  const prompt = freeKickPromptOverride ?? (await getPrompt('free-kick'))

  const [courseConfig, metaActions, availableMaterials, retrievedKnowledge, dialogHistory, customerBehavior, customerPortrait, temporalInformation] = await Promise.all([
    ContextBuilder.courseConfigFreeKick(chatId),
    Promise.resolve(''),
    extractMaterialSuggestion(mainTask, chatId, roundId, params.timePoint),
    KnowledgeRag.search(mainTask, chatId, roundId),
    SalesNodeHelper.getChatHistory(chatId, 2, 6, false),
    ContextBuilder.getCustomerBehavior(chatId),
    ContextBuilder.customerPortrait(chatId),
    ContextBuilder.temporalInformation(chatId),
  ])

  const payload = {
    courseConfig,
    metaActions,
    availableMaterials,
    retrievedKnowledge,
    customerBehavior,
    customerPortrait,
    dialogHistory,
    temporalInformation,
    mainTask,
  }

  const rawOutput = await LLM.predict(
    prompt,
    {
      responseJSON: true,
      meta: {
        promptName: 'free_kick',
        chat_id: chatId,
        round_id: roundId,
      },
    },
    payload,
  )

  let parsed: any = {}
  try {
    parsed = JSON.parse(rawOutput)
  } catch {
    parsed = {}
  }

  return {
    payload,
    rawOutput,
    parsed,
  }
}

function resolveMainTask(parsedPlanner: any): string {
  if (!parsedPlanner || typeof parsedPlanner !== 'object') return ''
  const plans = parsedPlanner.plans
  if (!plans || typeof plans !== 'object') return ''

  const candidate = plans.toAdd?.[0]
  if (typeof candidate === 'string') {
    return candidate
  }
  if (candidate && typeof candidate === 'object' && 'content' in candidate) {
    return candidate.content as string
  }

  const updateCandidate = plans.toUpdate?.[0]
  if (updateCandidate && typeof updateCandidate === 'object' && 'content' in updateCandidate) {
    return updateCandidate.content as string
  }

  if (Array.isArray(plans.toMerge) && plans.toMerge.length > 0) {
    const mergeCandidate = plans.toMerge[0]
    if (mergeCandidate && typeof mergeCandidate.mergedContent === 'string') {
      return mergeCandidate.mergedContent
    }
  }

  return ''
}

export async function runPlannerFreeKickFlow(params: PlannerFreeKickParams): Promise<PlannerResult> {
  const { chatId, timePoint, mainTaskOverride } = params
  const roundId = params.roundId ?? randomUUID()

  if (!chatId) {
    throw new Error('chatId 不能为空')
  }

  const trace: ExecutionTrace = []

  trace.push({
    nodeId: 'planner-mode',
    nodeType: 'modeNode',
    inputData: null,
    outputData: {
      chatId,
      timePoint,
      roundId,
    },
  })

  const plannerResult = await runPlannerStep(params)
  trace.push({
    nodeId: 'planner-prompt',
    nodeType: 'promptNode',
    inputData: plannerResult.context,
    outputData: {
      rawOutput: plannerResult.rawOutput,
      parsed: plannerResult.parsed,
    },
  })

  let mainTask = mainTaskOverride || resolveMainTask(plannerResult.parsed)
  if (!mainTask) {
    mainTask = '帮客户制定今晚的跟进计划'
  }

  const freeKickResult = await runFreeKickStep(params, mainTask, roundId)
  trace.push({
    nodeId: 'freekick-prompt',
    nodeType: 'promptNode',
    inputData: freeKickResult.payload,
    outputData: {
      rawOutput: freeKickResult.rawOutput,
      parsed: freeKickResult.parsed,
    },
  })

  trace.push({
    nodeId: 'planner-output',
    nodeType: 'outputNode',
    inputData: {
      planner: plannerResult.parsed,
      freeKick: freeKickResult.parsed,
      mainTask,
    },
    outputData: {
      planner: plannerResult.parsed,
      freeKick: freeKickResult.parsed,
      mainTask,
    },
  })

  return {
    trace,
    mainTask,
    context: plannerResult.context,
    plannerOutput: plannerResult.parsed,
    freeKickOutput: freeKickResult.parsed,
    roundId,
  }
}
