import { Job, Worker } from 'bullmq'
import { ChatHistoryService } from '../chat_history/chat_history'
import {
  Action,
  ActionType,
  ContentCustom,
  ContentDynamicPrompt,
  ContentFile,
  ContentImage,
  ContentLink,
  ContentTextPlain,
  ContentVideo,
  ContentVideoChannel,
  ContentVoice,
  ISendMedia,
  ITask,
  LinkSourceType,
  TextType
} from './visualized_sop_type'
import { getVisualizedSopQueueName, VisualizedSopTasks } from './visualized_sop_task_starter'
import { sleep } from 'openai/core'
import { Config } from '../../../../config/config'
import { IWecomMsgType, IWecomTextMsg } from '../../../../lib/juzi/type'
import { AsyncLock } from '../../../../lib/lock/lock'
import logger from '../../../../model/logger/logger'
import { RedisDB } from '../../../../model/redis/redis'
import { ChatDB } from '../../database/chat'
import { commonSleep } from '../flow/schedule/task/baseTask'
import { MessageSender } from '../message/message_send'
import { ContextBuilder } from '../agent/context'
import { SalesNodeHelper } from '../flow/helper/salesNodeHelper'
import { LLM } from '../../../../lib/ai/llm/LLM'
import { SystemMessagePromptTemplate } from '@langchain/core/prompts'
import { XMLHelper } from '../../../../lib/xml/xml'
import { ChatStatStoreManager } from '../../storage/chat_state_store'
import { randomSleep } from '../../../../lib/schedule/schedule'
import { PrismaMongoClient } from '../../../../model/mongodb/prisma'

export abstract class VisualizedSopProcessor {

  abstract getActionCustomMap():Record<string, (params:{chatId:string;userId:string})=> Promise<void>>
  abstract getConditionJudgeMap():Record<string, ((params:{chatId:string;userId:string})=>Promise<boolean>)>
  abstract getLinkSourceVariableTagMap():Record<string, (params:{chatId:string;userId:string})=>Promise<string>>
  abstract getTextVariableMap():Record<string, (params:{chatId:string;userId:string})=> Promise<string>>
  abstract handleActionDynamicPrompt(chatId: string, userId: string, action:ContentDynamicPrompt, opt: HandleActionOption): Promise<void>

  public start() {
    if (!Config.setting.wechatConfig?.id) {
      throw ('获取wx bot id错误')
    }
    new Worker(getVisualizedSopQueueName(Config.setting.wechatConfig.id), async (job: Job<ITask>) => {
      try {
        await this.generalProcess(job)
      } catch (e) {
        logger.error(`任务执行出错: ${job.name} ${job.data} ${e}`)
      }
    }, { connection: RedisDB.getInstance(),
      lockDuration: 60 * 1000,
      concurrency: 30
    }).on('error', (err) => {
      logger.error('visualized_sop_processor Worker 发生未捕获错误', err)
    })
  }

  public async generalProcess(job: Job<ITask>) {
    if (job.opts.delay && job.opts.delay < 0) {
      // logger.warn(`任务 ${job.name} 超时 ${-job.opts.delay} 毫秒，不进行处理`)
      return
    }
    const redisClient = RedisDB.getInstance()
    const isMemberResult = redisClient.sismember(`moer:sop_disable:${job.data.chatId}`, job.data.name)
    if (!isMemberResult) {
      logger.log(`${job.data.chatId} 执行sop ${job.data.name} 被禁`)
      return
    }

    try {
      const chat = await ChatDB.getById(job.data.chatId)
      if ((!chat) || (chat && chat.is_stop_group_push)) {
        return
      }

      // 如果最后一条消息是非营销消息，时间在 1 分钟内，非闲聊节点，延迟进行消息发送，最多重试 3 次。
      let retryCount = 0
      let isLastMsgWithin1Minute = true
      while (retryCount < 3) {
        isLastMsgWithin1Minute = await ChatHistoryService.isLastMessageWithDuration(chat.id, 1, 'minute')
        if (!isLastMsgWithin1Minute) {
          break
        }
        await sleep(60 * 1000)
        retryCount++
      }

      const task = job.data
      await ChatStatStoreManager.initState(task.chatId)

      // 加锁，防止在 主动回复中间插入消息
      const lock = new AsyncLock()
      await lock.acquire(job.data.chatId, async () => {
        await this.handleSopBySopId(task.chatId, task.userId, task.name)
      }, { timeout: 2 * 60 * 1000 })
    } catch (e) {
      logger.error(e)
    }
  }

  public async handleSopBySopId(chatId:string, userId:string, sopId:string, force:boolean = false) {
    const sops = await VisualizedSopTasks.getTasks(Config.setting.wechatConfig!.id)
    const filtedSops = sops.filter((item) => {
      if (item.id != sopId) return false
      return true
    })
    for (const sop of filtedSops) {
      // 将所有条件取出来一次性判断
      await this.handleSop(chatId, userId, sop, { sop_id:sop.id, force })
    }
  }

  public  async handleSop(chatId:string, userId:string, sop:Awaited<ReturnType<typeof VisualizedSopTasks.getTasks>>[number], opt?:HandleActionOption) {
    logger.log(`客户${chatId}执行sop${sop.id} ${sop.title} ${sop.week}周${sop.day}天${sop.time}`)
    const conditionFixedTypes = new Set<string>()
    const conditionDynamicTypes = new Set<string>()
    for (const { conditions } of sop.situations) {
      for (const condition of conditions) {
        if (condition.type == 'fixed') {
          conditionFixedTypes.add(condition.condition)
        } else {
          conditionDynamicTypes.add(condition.condition)
        }
      }
    }
    const conditionsFixed = [...conditionFixedTypes]
    const conditionsDynamic = [...conditionDynamicTypes]
    const conditionFixedMap = new Map<string, boolean>()
    const conditionDynamicMap = new Map<string, boolean>()
    for (const condition of conditionsFixed) {
      conditionFixedMap.set(condition, await this.judgeFixedCondition(chatId, userId, condition))
    }
    for (const condition of conditionsDynamic) {
      conditionDynamicMap.set(condition, await this.judgeDynamicCondition(chatId, userId, condition))
    }
    logger.log(`客户${chatId}执行sop${sop.id} 时候固定条件是 ${JSON.stringify(Object.fromEntries(conditionFixedMap))} 动态条件是 ${JSON.stringify(Object.fromEntries(conditionDynamicMap))}`)

    for (const situation of sop.situations) {
      if (!this.judgeConditions(situation.conditions, conditionFixedMap, conditionDynamicMap)) continue

      for (const action of situation.action) {
        await this.handleAction(chatId, userId, action as Action, opt)
        await commonSleep()
      }
    }
  }

  public async judgeFixedCondition(chatId:string, userId:string, condition:string):Promise<boolean> {
    const func = this.getConditionJudgeMap()[condition]
    if (!func) throw (`判断条件没有这个键${condition}`)
    return await func({ userId, chatId })
  }

  public async judgeDynamicCondition(chatId:string, userId:string, condition:string): Promise<boolean> {
    const userPortrait = await ContextBuilder.getCustomerPortrait(chatId)
    const userBehavior = await ContextBuilder.getCustomerBehavior(chatId)
    const chatHistory = await SalesNodeHelper.getChatHistory(chatId, 6, 18)
    const result = await LLM.predict(VisualizedSopProcessor.getThinkPrompt(),
      {
        meta: {
          promptName: 'sop_condition_think',
          chat_id: chatId,
        }
      },
      {
        userPortrait: userPortrait,
        userBehavior: userBehavior,
        chatHistory: chatHistory,
        condition:condition
      }
    )
    const judge = XMLHelper.extractContent(result, 'judge') || ''
    return judge == 'true'
  }
  private static getThinkPrompt() {
    return SystemMessagePromptTemplate.fromTemplate(`# 角色设定
- 你是顶级销售，擅长根据对话记录，客户行为和客户画像，判断条件是否是正确的

## 主要任务
- 思考（think）总结聊天记录，判断是否和条件内容相关，结合客户行为和客户画像判断条件是否正确
- 判断（judge）只输出"true"或"false",条件正确则输出"true",条件错误则输出"false"

{{userBehavior}}

{{userPortrait}}

{{chatHistory}}

## 条件
{{condition}}

## 格式要求
- 请先将思考输出到 <think></think> 标签中
- 然后将判断输出到 <judge></judge> 标签中`,
    { templateFormat: 'mustache' })
  }

  public judgeConditions(conditions:{
        isOrNotIs: boolean
        type:string
        condition: string
    }[], conditionFixedMap:Map<string, boolean>, conditionDynamicMap:Map<string, boolean>) {
    for (const condition of conditions) {
      let conditionMap = new Map<string, boolean>()
      if (condition.type == 'dynamic') {
        conditionMap = conditionDynamicMap
      } else {
        conditionMap = conditionFixedMap
      }
      if (!conditionMap.has(condition.condition)) {
        logger.log('没有这个条件')
        return false
      }
      if ((condition.isOrNotIs && !conditionMap.get(condition.condition)) || (!condition.isOrNotIs && conditionMap.get(condition.condition))) {
        return false
      }
    }
    return true
  }

  public async handleAction(chatId:string, userId:string, action:Action, opt:HandleActionOption = { force:false }) {
    try {
      if (action.type == ActionType.text) {
        await this.handleActionText(chatId, userId, action, opt)
      } else if (action.type == ActionType.image) {
        await this.handleActionImage(chatId, userId, action, opt)
      } else if (action.type == ActionType.video) {
        await this.handleActionVideo(chatId, userId, action, opt)
      } else if (action.type == ActionType.file) {
        await this.handleActionFile(chatId, userId, action, opt)
      } else if (action.type == ActionType.voice) {
        await this.handleActionVoice(chatId, userId, action, opt)
      } else if (action.type == ActionType.custom) {
        await this.handleActionCustom(chatId, userId, action)
      } else if (action.type == ActionType.link) {
        await this.handleActionLink(chatId, userId, action, opt)
      } else if (action.type == ActionType.videoChannel) {
        await this.handleActionVideoChannel(chatId, userId, action, opt)
      } else if (action.type == ActionType.dynamicPrompt) {
        await randomSleep(0, 1000 * 60)
        await this.handleActionDynamicPrompt(chatId, userId, action, opt)
      } else {
        throw ('unknow action type')
      }
    } catch (e) {
      logger.error(`sop的时候action出错${e}`)
    }
  }

  public async handleActionText(chatId:string, userId:string, action: ContentTextPlain, opt:HandleActionOption) {
    let text = ''
    for (const item of action.textList) {
      if (item.type == TextType.fixed) {
        text += item.text
      } else {
        text += await this.getTextVariable(chatId, userId, item.tag)
      }
    }
    await sendMsg(userId, chatId, text.trim(), action.description, opt.force, undefined, opt.sop_id)
  }

  public async getTextVariable(chatId:string, userId:string, variableTag:string):Promise<string> {
    const func = this.getTextVariableMap()[variableTag]
    if (!func) throw (`字符串变量没有这个键${variableTag}`)
    return await func({ userId, chatId })
  }

  public async handleActionImage(chatId:string, userId:string, action: ContentImage, opt:HandleActionOption) {
    await sendMsg(userId, chatId, [<ISendMedia>{
      description:action.description,
      msg:{
        type: IWecomMsgType.Image,
        url:action.url
      },
    }], action.description, opt.force)
  }

  public async handleActionVideo(chatId:string, userId:string, action: ContentVideo, opt:HandleActionOption) {
    await sendMsg(userId, chatId, [<ISendMedia>{
      description:action.description,
      msg:{
        type: IWecomMsgType.Video,
        url:action.url
      }
    }], action.description, opt.force, undefined, opt.sop_id)
  }

  public async handleActionVoice(chatId:string, userId:string, action: ContentVoice, opt:HandleActionOption) {
    await sendMsg(userId, chatId, [<ISendMedia>{
      description:action.description,
      msg:{
        type: IWecomMsgType.Voice,
        voiceUrl:action.url,
        duration:action.duration
      }
    }], action.description, opt.force, undefined, opt.sop_id)
  }

  public async handleActionFile(chatId:string, userId:string, action: ContentFile, opt:HandleActionOption) {
    await sendMsg(userId, chatId, [<ISendMedia>{
      description:action.description,
      msg:{
        type: IWecomMsgType.File,
        name:action.name,
        url:action.url
      }
    }], action.description, opt.force, undefined, opt.sop_id)
  }

  public async handleActionCustom(chatId:string, userId:string, action: ContentCustom):Promise<void> {
    const func = this.getActionCustomMap()[action.tag]
    if (!func) throw (`自定义事件没有这个键${action.tag}`)
    return await func({ userId, chatId })
  }

  public async handleActionLink(chatId:string, userId:string, action: ContentLink, opt:HandleActionOption) {
    let sourceLink = ''
    if (action.source.type == LinkSourceType.fixed) {
      sourceLink = action.source.url
    } else if (action.source.type == LinkSourceType.variable) {
      const getSourceLinkFunc = this.getLinkSourceVariableTagMap()[action.source.tag]
      if (!getSourceLinkFunc) throw (`自定义事件没有这个键${action.source.tag}`)
      sourceLink = await getSourceLinkFunc({ chatId, userId })
    } else {
      throw ('unknow link source type')
    }
    await sendMsg(userId, chatId, [<ISendMedia>{
      description: action.description,
      msg: {
        type: IWecomMsgType.Link,
        sourceUrl: sourceLink,
        title: action.title,
        summary: action.summary,
        imageUrl:action.imageUrl
      }
    }], action.description, opt.force, undefined, opt.sop_id)
  }

  public async handleActionVideoChannel(chatId:string, userId:string, action:ContentVideoChannel, opt: HandleActionOption) {
    await sendMsg(userId, chatId, [<ISendMedia>{
      description:action.description,
      msg:{
        type: IWecomMsgType.VideoChannel,
        avatarUrl: action.avatarUrl,
        coverUrl: action.coverUrl,
        description: action.contentDescription,
        feedType:4,
        nickname: action.nickname,
        thumbUrl: action.thumbUrl,
        url: action.url,
        extras: action.extras
      }
    }], action.description, opt.force, undefined, opt.sop_id)
  }
}

export interface HandleActionOption {
  force: boolean
  sop_id?:string
}

export async function sendMsg(userId: string, chatId: string,  messages: (ISendMedia | string)[] | string, description?: string, isRepeatedCheck?: boolean, roundId?:string, sop_id?:string): Promise<void> {
  // 这里注意下，所有 消息重复的话都不进行发送了
  if (typeof messages === 'string') {
    const isRepeated = await ChatHistoryService.hasRepeatedMsg(chatId, messages)
    if (isRepeated && !isRepeatedCheck) {
      return
    }

    await MessageSender.sendById({
      user_id: userId,
      chat_id: chatId,
      ai_msg: messages,
    }, {
      shortDes: description ? `[${description}]` : undefined,
      round_id: roundId,
      sop_id: sop_id
    })
    return
  }

  for (let i = 0; i < messages.length; i++) {
    const message = messages[i]
    if (typeof message === 'string') {
      const isRepeated = await ChatHistoryService.hasRepeatedMsg(chatId, message)
      if (isRepeated && !isRepeatedCheck) {
        return
      }

      await MessageSender.sendById({
        user_id: userId,
        chat_id: chatId,
        ai_msg: message,
      }, {
        shortDes: description ? `[${description}]` : undefined,
        round_id:roundId,
        sop_id:sop_id,
      })
    } else {
      // 文件，图片类型根据描述去重
      let checkRepeatedText = message.description

      // 对文本不进行根据描述去重
      if (message.msg.type === IWecomMsgType.Text) {
        checkRepeatedText = (message.msg as IWecomTextMsg).text
      }

      const isRepeated = await ChatHistoryService.hasRepeatedMsg(chatId, checkRepeatedText)
      if (isRepeated && !isRepeatedCheck) {
        return
      }

      await MessageSender.sendById({
        user_id: userId,
        chat_id: chatId,
        ai_msg: `[${message.description}]`,
        send_msg: message.msg,
      }, {
        shortDes: `[${ message.description  }]`,
        round_id:roundId,
        sop_id:sop_id
      })
    }

    if (i < messages.length - 1) {
      await commonSleep()
    }
  }
}

/**
 * 发送素材消息
 * @param userId 客户ID
 * @param chatId 聊天ID
 * @param material 素材对象
 * @param isRepeatedCheck 是否检查重复
 * @param roundId 轮次ID
 * @param sop_id SOP ID
 */
export async function sendMaterial(userId: string, chatId: string, material: Material, isRepeatedCheck?: boolean, roundId?: string, sop_id?: string): Promise<void> {
  const description = material.description

  // 101: 文本类型
  if (material.type === 101) {
    if (material.data && typeof material.data === 'object') {
      const data = material.data as any
      const content = data.content || material.title
      await sendMsg(userId, chatId, content, description, isRepeatedCheck, roundId, sop_id)
    }
    return
  }

  // 多媒体类型: 102(图片), 103(视频), 104(文件)
  if ([102, 103, 104].includes(material.type)) {
    if (material.data && typeof material.data === 'object') {
      const data = material.data as any
      if (!data.url) {
        console.error(`素材 ${material.id} 缺少文件URL`)
        return
      }

      let mediaMessage: ISendMedia

      if (material.type === 102) {
        // 图片类型
        mediaMessage = {
          description: description,
          msg: {
            type: IWecomMsgType.Image,
            url: data.url
          }
        }
      } else if (material.type === 103) {
        // 视频类型
        mediaMessage = {
          description: description,
          msg: {
            type: IWecomMsgType.Video,
            url: data.url
          }
        }
      } else if (material.type === 106) {
        // 链接类型
        mediaMessage = {
          description: description,
          msg: {
            type: IWecomMsgType.Link,
            sourceUrl: data.url,
            title: data.title,
            summary: data.description,
            imageUrl: data.thumbnailUrl
          }
        }
      } else if (material.type === 107) {
        //视频号
        mediaMessage = {
          description: description,
          msg: {
            type: IWecomMsgType.VideoChannel,
            avatarUrl: data.avatarUrl,
            coverUrl: data.coverUrl,
            description: data.description,
            extras: data.extras,
            feedType: 4,
            nickname: data.nickname,
            thumbUrl: data.thumbUrl,
            url: data.url
          }
        }
      } else if (material.type === 104) {
        // 文件类型
        // 从fileName中提取后缀，与title组合
        let fileName = material.title
        if (data.fileName) {
          const extension = (data.fileName as string).split('.').pop()
          if (extension) {
            fileName = `${material.title}.${extension}`
          }
        }

        mediaMessage = {
          description: description,
          msg: {
            type: IWecomMsgType.File,
            name: fileName,
            url: data.url
          }
        }
      } else {
        console.error(`不支持的素材类型: ${material.type}`)
        return
      }

      await sendMsg(userId, chatId, [mediaMessage], description, isRepeatedCheck, roundId, sop_id)
    } else {
      console.error(`素材 ${material.id} 缺少文件数据`)
    }
    return
  }

  console.error(`不支持的素材类型: ${material.type}`)
}

/**
 * 通过素材ID发送素材
 * @param userId 客户ID
 * @param chatId 聊天ID
 * @param materialId 素材ID
 * @param isRepeatedCheck 是否检查重复
 * @param roundId 轮次ID
 * @param sop_id SOP ID
 */
export async function sendMaterialById(userId: string, chatId: string, materialId: string, isRepeatedCheck?: boolean, roundId?: string, sop_id?: string): Promise<void> {
  try {
    // 从数据库获取素材信息
    const material = await PrismaMongoClient.getInstance().material.findUnique({
      where: { id: materialId }
    })

    if (!material) {
      console.error(`素材不存在: ${materialId}`)
      return
    }

    // 检查素材是否启用
    if (!material.enable) {
      console.error(`素材未启用: ${materialId}`)
      return
    }

    // 调用sendMaterial发送素材
    await sendMaterial(userId, chatId, material as Material, isRepeatedCheck, roundId, sop_id)
  } catch (error) {
    console.error(`发送素材失败 ${materialId}:`, error)
  }
}

export interface Material {
  id: string
  type: number
  title: string
  description: string
  main_category: string
  sub_category: string
  doc: string
  data: any
  enable: boolean
  es_id: string
  created_at?: Date
}